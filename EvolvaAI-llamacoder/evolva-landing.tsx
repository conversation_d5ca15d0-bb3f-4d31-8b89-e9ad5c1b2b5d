import React, { useState } from 'react'

export default function EvolvaLanding() {
  const [activeNav, setActiveNav] = useState('Home')

  const techCards = [
    { name: 'OpenAI', icon: '🤖' },
    { name: 'n8n', icon: '🔗' },
    { name: '<PERSON>', icon: '✨' },
    { name: 'python', icon: '🐍' },
    { name: 'Relevance AI', icon: '🎯' },
    { name: 'supabase', icon: '⚡' },
    { name: 'APIFY', icon: '🚀' },
    { name: 'OpenAI', icon: '🤖' },
  ]

  const navItems = ['Home', 'Services', 'Contact', 'FAQ']

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#1f2937',
      color: 'white',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '24px 32px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{ width: '40px', height: '40px', position: 'relative' }}>
            {/* Network/Molecule Icon */}
            <svg viewBox="0 0 40 40" style={{ width: '100%', height: '100%', color: '#FFBF00' }}>
              <circle cx="8" cy="8" r="3" fill="currentColor" />
              <circle cx="32" cy="8" r="3" fill="currentColor" />
              <circle cx="20" cy="20" r="3" fill="currentColor" />
              <circle cx="8" cy="32" r="3" fill="currentColor" />
              <circle cx="32" cy="32" r="3" fill="currentColor" />
              <line x1="8" y1="8" x2="20" y2="20" stroke="currentColor" strokeWidth="2" />
              <line x1="32" y1="8" x2="20" y2="20" stroke="currentColor" strokeWidth="2" />
              <line x1="8" y1="32" x2="20" y2="20" stroke="currentColor" strokeWidth="2" />
              <line x1="32" y1="32" x2="20" y2="20" stroke="currentColor" strokeWidth="2" />
            </svg>
          </div>
          <span style={{ fontSize: '24px', fontWeight: 'bold' }}>Evolva AI</span>
        </div>

        {/* Navigation */}
        <nav style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          backgroundColor: '#374151',
          borderRadius: '9999px',
          padding: '8px'
        }}>
          {navItems.map((item) => (
            <button
              key={item}
              onClick={() => setActiveNav(item)}
              style={{
                padding: '8px 16px',
                borderRadius: '9999px',
                fontSize: '14px',
                fontWeight: '500',
                border: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s',
                backgroundColor: activeNav === item ? '#4b5563' : 'transparent',
                color: activeNav === item ? 'white' : '#d1d5db'
              }}
            >
              {item}
            </button>
          ))}
        </nav>

        {/* Get Started Button */}
        <button style={{
          backgroundColor: 'white',
          color: '#1f2937',
          padding: '8px 24px',
          borderRadius: '9999px',
          fontWeight: '500',
          border: 'none',
          cursor: 'pointer',
          transition: 'background-color 0.2s'
        }}>
          Get Started →
        </button>
      </header>

      {/* Hero Section */}
      <section style={{
        textAlign: 'center',
        padding: '80px 32px'
      }}>
        <h1 style={{
          fontSize: '96px',
          fontWeight: 'bold',
          marginBottom: '24px',
          lineHeight: '1'
        }}>
          Artificial Intelligence
        </h1>
        <h2 style={{
          fontSize: '80px',
          fontWeight: 'bold',
          color: '#6b7280',
          marginBottom: '32px',
          letterSpacing: '0.1em',
          lineHeight: '1'
        }}>
          For Your Business
        </h2>
        <p style={{
          fontSize: '20px',
          color: '#9ca3af',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          We help growing companies implement AI to scale without hiring staff.
        </p>
      </section>

      {/* Technology Cards */}
      <section style={{ padding: '48px 32px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '16px',
          overflowX: 'auto',
          paddingBottom: '20px'
        }}>
          {techCards.map((tech, index) => (
            <div
              key={index}
              style={{
                backgroundColor: '#FFBF00',
                border: 'none',
                minWidth: '180px',
                height: '128px',
                flexShrink: 0,
                borderRadius: '12px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '24px'
              }}
            >
              <div style={{
                fontSize: '32px',
                marginBottom: '8px',
                color: '#1f2937'
              }}>
                {tech.icon}
              </div>
              <span style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#1f2937'
              }}>
                {tech.name}
              </span>
            </div>
          ))}
        </div>
      </section>

      {/* Services Section */}
      <section style={{
        textAlign: 'center',
        padding: '80px 32px'
      }}>
        <h2 style={{
          fontSize: '80px',
          fontWeight: 'bold',
          color: '#6b7280',
          marginBottom: '16px'
        }}>
          Services
        </h2>
        <h3 style={{
          fontSize: '48px',
          fontWeight: 'bold',
          color: 'white',
          marginBottom: '48px',
          lineHeight: '1.2'
        }}>
          Your AI Advantage<br />
          Starts Here
        </h3>
        
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <div style={{
            backgroundColor: '#374151',
            borderRadius: '16px',
            padding: '32px',
            marginBottom: '32px'
          }}>
            <h4 style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: 'white',
              marginBottom: '16px'
            }}>
              AI Strategy & Roadmap Design
            </h4>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <p style={{
                color: '#9ca3af',
                textAlign: 'left',
                flex: 1,
                fontSize: '16px',
                lineHeight: '1.6'
              }}>
                We analyze your business processes and create a comprehensive AI implementation strategy 
                tailored to your specific needs and goals.
              </p>
              <div style={{ marginLeft: '32px' }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  backgroundColor: '#FFBF00',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <span style={{
                    fontSize: '24px',
                    color: '#1f2937'
                  }}>
                    📋
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{
        backgroundColor: '#374151',
        padding: '48px 32px',
        marginTop: '80px'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center'
        }}>
          <p style={{ color: '#9ca3af' }}>
            © 2024 Evolva AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}